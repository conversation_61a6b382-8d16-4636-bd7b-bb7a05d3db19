import logging
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, Form, Request, HTTPException
from fastapi.responses import FileResponse
import tempfile
import soundfile as sf
from typing import Optional
import shutil
import os
from src.f5_tts_api.f5tts_wrapper import F5TTSWrapper
from f5_tts.infer.utils_infer import preprocess_ref_audio_text, transcribe

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI(title="F5-TTS Web API")

# Initialize TTS wrapper with default F5-TTS model
tts = F5TTSWrapper(model_type="F5-TTS_v1")

@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info("Incoming request: %s %s", request.method, request.url)
    response = await call_next(request)
    logger.info("Completed request with status code: %d", response.status_code)
    return response

@app.post("/synthesize/")
async def synthesize_speech(
    ref_audio: UploadFile,
    ref_text: Optional[str] = Form(None),
    gen_text: str = Form(...),
    model_type: Optional[str] = Form("F5-TTS_v1"),
    remove_silence: Optional[bool] = Form(False),
    seed: Optional[int] = Form(-1),
    cross_fade_duration: Optional[float] = Form(0.15),
    nfe_step: Optional[int] = Form(32),
    speed: Optional[float] = Form(1.0),
):
    """
    Synthesize speech using F5-TTS with enhanced parameters following infer_gradio structure

    Args:
        ref_audio: Reference audio file
        ref_text: Reference text (if empty, will be transcribed)
        gen_text: Text to generate
        model_type: Model type to use ("F5-TTS_v1" or "E2-TTS")
        remove_silence: Whether to remove silence from output
        seed: Random seed (-1 for random)
        cross_fade_duration: Cross-fade duration between segments
        nfe_step: Number of denoising steps
        speed: Speed multiplier
    """
    logger.info("Endpoint '/synthesize/' called.")
    logger.info("Received file: %s", ref_audio.filename)
    logger.info("ref_text: %s", ref_text)
    logger.info("gen_text preview: %s", gen_text[:50])
    logger.info("Parameters - model_type: %s, remove_silence: %s, seed: %s, cross_fade_duration: %s, nfe_step: %s, speed: %s",
                model_type, remove_silence, seed, cross_fade_duration, nfe_step, speed)

    try:
        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(ref_audio.file, tmp_audio)
            tmp_audio_path = tmp_audio.name
            logger.info("Saved reference audio to: %s", tmp_audio_path)

        # Use the new infer method with enhanced parameters
        (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = tts.infer(
            ref_audio_orig=tmp_audio_path,
            ref_text=ref_text,
            gen_text=gen_text,
            model_type=model_type,
            remove_silence=remove_silence,
            seed=seed,
            cross_fade_duration=cross_fade_duration,
            nfe_step=nfe_step,
            speed=speed,
            show_info=logger.info,
        )

        logger.info("Synthesis successful. Sample rate: %d, Used seed: %s", sample_rate, used_seed)
        logger.info("Processed ref_text: %s", processed_ref_text)
        if spectrogram_path:
            logger.info("Spectrogram saved to: %s", spectrogram_path)

        # Save output audio
        out_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
        sf.write(out_path, audio_data, sample_rate)
        logger.info("Generated speech saved to: %s", out_path)

        # Clean up temporary reference audio file
        try:
            os.unlink(tmp_audio_path)
        except OSError:
            logger.warning("Could not delete temporary file: %s", tmp_audio_path)

        return FileResponse(out_path, media_type="audio/wav", filename="output.wav")

    except Exception as e:
        logger.exception("Error during speech synthesis: %s", str(e))
        # Clean up temporary files on error
        try:
            if 'tmp_audio_path' in locals():
                os.unlink(tmp_audio_path)
        except OSError:
            pass
        raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {str(e)}")

@app.post("/transcribe/")
async def transcribe_audio(
    audio: UploadFile,
    language: Optional[str] = Form(None),
):
    """
    Transcribe audio using the same pipeline as reference text processing

    Args:
        audio: Audio file to transcribe
        language: Optional language code for transcription (e.g., "en", "es", "fr")

    Returns:
        JSON response with transcribed text
    """
    logger.info("Endpoint '/transcribe/' called.")
    logger.info("Received file: %s", audio.filename)
    logger.info("Language: %s", language)

    try:
        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(audio.file, tmp_audio)
            tmp_audio_path = tmp_audio.name
            logger.info("Saved audio to: %s", tmp_audio_path)

        # Use the same preprocessing as reference audio (includes clipping and silence removal)
        processed_audio_path, _ = preprocess_ref_audio_text(tmp_audio_path, "", show_info=logger.info)
        logger.info("Preprocessed audio saved to: %s", processed_audio_path)

        # Transcribe the processed audio
        transcribed_text = transcribe(processed_audio_path, language=language)
        logger.info("Transcription successful: %s", transcribed_text[:100] + "..." if len(transcribed_text) > 100 else transcribed_text)

        # Clean up temporary files
        try:
            os.unlink(tmp_audio_path)
            # Note: processed_audio_path might be cached, so we don't delete it
        except OSError:
            logger.warning("Could not delete temporary file: %s", tmp_audio_path)

        return {
            "transcribed_text": transcribed_text,
            "language": language,
            "status": "success"
        }

    except Exception as e:
        logger.exception("Error during transcription: %s", str(e))
        # Clean up temporary files on error
        try:
            if 'tmp_audio_path' in locals():
                os.unlink(tmp_audio_path)
        except OSError:
            pass
        raise HTTPException(status_code=500, detail=f"Transcription failed: {str(e)}")

@app.get("/")
def root():
    logger.info("Endpoint '/' called.")
    return {
        "message": "Welcome to the F5-TTS Web API",
        "version": "2.0",
        "features": [
            "Enhanced synthesis with infer_gradio structure",
            "Multiple model support (F5-TTS_v1, E2-TTS)",
            "Advanced parameters (speed, cross_fade_duration, nfe_step)",
            "Silence removal",
            "Spectrogram generation",
            "Audio transcription using FasterWhisper",
            "Legacy compatibility"
        ],
        "endpoints": {
            "/synthesize/": "Enhanced synthesis with advanced parameters",
            "/synthesize_legacy/": "Legacy synthesis for backward compatibility",
            "/transcribe/": "Transcribe audio to text using FasterWhisper",
            "/": "API information"
        }
    }
